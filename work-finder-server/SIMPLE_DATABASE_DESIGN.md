# WORKFINDER SERVER - SIMPLE DATABASE DESIGN
## Thiế<PERSON> kế Database Đơn Giản cho Đồ Án Tốt Nghiệp

---

## OVERVIEW

**Mục tiêu:** Thiết kế database đơn g<PERSON>, đ<PERSON> chức năng cơ bản cho website tuyển dụng
**Số lượng entities:** 10 entities chính
**Phạm vi:** Chức năng core của job portal

---

## 10 ENTITIES CHÍNH

### 1. **USER** (Người dùng)
**Table:** `users`
**Mục đích:** Quản lý tài khoản người dùng (ứng viên, nhà tuyển dụng)

```sql
user_id: SERIAL PRIMARY KEY
email: VARCHAR(254) NOT NULL UNIQUE
password: VARCHAR(255) NOT NULL -- Hashed
full_name: VARCHAR(150)
phone: VARCHAR(25)
role: ENUM('job_seeker', 'recruiter', 'admin') DEFAULT 'job_seeker'
avatar: VARCHAR(255)
created_at: TIMESTAMP DEFAULT NOW()
```

**Relationships:**
- 1:N → Resume
- 1:N → Application  
- 1:N → SavedJob
- 1:N → Company (as recruiter)

---

### 2. **COMPANY** (Công ty)
**Table:** `companies`
**Mục đích:** Thông tin công ty tuyển dụng

```sql
company_id: SERIAL PRIMARY KEY
company_name: VARCHAR(200) NOT NULL
description: TEXT
industry: VARCHAR(100)
website: VARCHAR(255)
logo: VARCHAR(255)
address: VARCHAR(500)
recruiter_id: INTEGER REFERENCES users(user_id)
created_at: TIMESTAMP DEFAULT NOW()
```

**Relationships:**
- N:1 → User (recruiter)
- 1:N → JobPost

---

### 3. **JOBPOST** (Bài đăng tuyển dụng)
**Table:** `job_posts`
**Mục đích:** Các vị trí tuyển dụng

```sql
job_id: SERIAL PRIMARY KEY
company_id: INTEGER REFERENCES companies(company_id)
job_title: VARCHAR(200) NOT NULL
description: TEXT NOT NULL
requirements: TEXT
location: VARCHAR(200)
salary_min: DECIMAL(10,2)
salary_max: DECIMAL(10,2)
job_type: ENUM('full_time', 'part_time', 'contract', 'internship')
status: ENUM('active', 'closed', 'draft') DEFAULT 'active'
posted_date: TIMESTAMP DEFAULT NOW()
application_deadline: TIMESTAMP
```

**Relationships:**
- N:1 → Company
- 1:N → Application
- 1:N → SavedJob

---

### 4. **RESUME** (Hồ sơ/CV)
**Table:** `resumes`
**Mục đích:** CV của ứng viên

```sql
resume_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
file_name: VARCHAR(255) NOT NULL
file_path: VARCHAR(500) NOT NULL
file_size: BIGINT
summary: TEXT
education: TEXT
experience: TEXT
skills: TEXT
uploaded_at: TIMESTAMP DEFAULT NOW()
```

**Relationships:**
- N:1 → User
- 1:N → Application

---

### 5. **APPLICATION** (Đơn ứng tuyển)
**Table:** `applications`
**Mục đích:** Đơn xin việc của ứng viên

```sql
application_id: SERIAL PRIMARY KEY
job_id: INTEGER REFERENCES job_posts(job_id)
user_id: INTEGER REFERENCES users(user_id)
resume_id: INTEGER REFERENCES resumes(resume_id)
cover_letter: TEXT
status: ENUM('pending', 'reviewing', 'accepted', 'rejected') DEFAULT 'pending'
applied_at: TIMESTAMP DEFAULT NOW()

UNIQUE(user_id, job_id) -- Không apply trùng
```

**Relationships:**
- N:1 → JobPost
- N:1 → User
- N:1 → Resume

---

### 6. **SAVEDJOB** (Job đã lưu)
**Table:** `saved_jobs`
**Mục đích:** Job mà ứng viên quan tâm

```sql
saved_job_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
job_id: INTEGER REFERENCES job_posts(job_id)
saved_at: TIMESTAMP DEFAULT NOW()

UNIQUE(user_id, job_id) -- Không lưu trùng
```

**Relationships:**
- N:1 → User
- N:1 → JobPost

---

### 7. **CATEGORY** (Danh mục nghề nghiệp)
**Table:** `categories`
**Mục đích:** Phân loại công việc

```sql
category_id: SERIAL PRIMARY KEY
category_name: VARCHAR(100) NOT NULL UNIQUE
description: TEXT
is_active: BOOLEAN DEFAULT true
created_at: TIMESTAMP DEFAULT NOW()
```

**Relationships:**
- 1:N → JobPost

---

### 8. **JOBCATEGORY** (Liên kết Job-Category)
**Table:** `job_categories`
**Mục đích:** Many-to-many giữa Job và Category

```sql
id: SERIAL PRIMARY KEY
job_id: INTEGER REFERENCES job_posts(job_id)
category_id: INTEGER REFERENCES categories(category_id)

UNIQUE(job_id, category_id)
```

**Relationships:**
- N:1 → JobPost
- N:1 → Category

---

### 9. **NOTIFICATION** (Thông báo)
**Table:** `notifications`
**Mục đích:** Thông báo cho người dùng

```sql
notification_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
title: VARCHAR(255) NOT NULL
message: TEXT NOT NULL
type: ENUM('application_status', 'new_job', 'system') DEFAULT 'system'
is_read: BOOLEAN DEFAULT false
created_at: TIMESTAMP DEFAULT NOW()
```

**Relationships:**
- N:1 → User

---

### 10. **REVIEW** (Đánh giá công ty)
**Table:** `reviews`
**Mục đích:** Đánh giá của ứng viên về công ty

```sql
review_id: SERIAL PRIMARY KEY
company_id: INTEGER REFERENCES companies(company_id)
user_id: INTEGER REFERENCES users(user_id)
rating: INTEGER CHECK (rating >= 1 AND rating <= 5)
title: VARCHAR(255)
comment: TEXT
is_anonymous: BOOLEAN DEFAULT false
created_at: TIMESTAMP DEFAULT NOW()

UNIQUE(user_id, company_id) -- Mỗi user chỉ review 1 lần/company
```

**Relationships:**
- N:1 → Company
- N:1 → User

---

## SIMPLIFIED ERD DIAGRAM

```mermaid
erDiagram
    USER {
        serial user_id PK
        varchar email UK
        varchar password
        varchar full_name
        varchar phone
        enum role
        varchar avatar
        timestamp created_at
    }

    COMPANY {
        serial company_id PK
        varchar company_name
        text description
        varchar industry
        varchar website
        varchar logo
        varchar address
        integer recruiter_id FK
        timestamp created_at
    }

    JOBPOST {
        serial job_id PK
        integer company_id FK
        varchar job_title
        text description
        text requirements
        varchar location
        decimal salary_min
        decimal salary_max
        enum job_type
        enum status
        timestamp posted_date
        timestamp application_deadline
    }

    RESUME {
        serial resume_id PK
        integer user_id FK
        varchar file_name
        varchar file_path
        bigint file_size
        text summary
        text education
        text experience
        text skills
        timestamp uploaded_at
    }

    APPLICATION {
        serial application_id PK
        integer job_id FK
        integer user_id FK
        integer resume_id FK
        text cover_letter
        enum status
        timestamp applied_at
    }

    SAVEDJOB {
        serial saved_job_id PK
        integer user_id FK
        integer job_id FK
        timestamp saved_at
    }

    CATEGORY {
        serial category_id PK
        varchar category_name UK
        text description
        boolean is_active
        timestamp created_at
    }

    JOBCATEGORY {
        serial id PK
        integer job_id FK
        integer category_id FK
    }

    NOTIFICATION {
        serial notification_id PK
        integer user_id FK
        varchar title
        text message
        enum type
        boolean is_read
        timestamp created_at
    }

    REVIEW {
        serial review_id PK
        integer company_id FK
        integer user_id FK
        integer rating
        varchar title
        text comment
        boolean is_anonymous
        timestamp created_at
    }

    %% Relationships
    USER ||--o{ COMPANY : "recruiter manages"
    USER ||--o{ RESUME : "has"
    USER ||--o{ APPLICATION : "submits"
    USER ||--o{ SAVEDJOB : "saves"
    USER ||--o{ NOTIFICATION : "receives"
    USER ||--o{ REVIEW : "writes"

    COMPANY ||--o{ JOBPOST : "posts"
    COMPANY ||--o{ REVIEW : "receives"

    JOBPOST ||--o{ APPLICATION : "receives"
    JOBPOST ||--o{ SAVEDJOB : "saved_as"
    JOBPOST ||--o{ JOBCATEGORY : "belongs_to"

    RESUME ||--o{ APPLICATION : "used_in"

    CATEGORY ||--o{ JOBCATEGORY : "categorizes"
```

---

## CORE FEATURES SUPPORTED

### 1. **User Management**
- Đăng ký/đăng nhập
- Phân quyền (job_seeker, recruiter, admin)
- Profile cơ bản

### 2. **Company Management**
- Tạo profile công ty
- Quản lý bởi recruiter
- Đánh giá từ ứng viên

### 3. **Job Management**
- Đăng tin tuyển dụng
- Phân loại theo category
- Tìm kiếm và filter
- Lưu job yêu thích

### 4. **Application Process**
- Upload CV
- Apply job với cover letter
- Theo dõi trạng thái application

### 5. **Notification System**
- Thông báo trạng thái application
- Thông báo job mới
- Thông báo hệ thống

---

## ESSENTIAL INDEXES

```sql
-- Performance cho search
CREATE INDEX idx_job_posts_location ON job_posts(location);
CREATE INDEX idx_job_posts_status ON job_posts(status);
CREATE INDEX idx_job_posts_posted_date ON job_posts(posted_date);

-- User queries
CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_saved_jobs_user_id ON saved_jobs(user_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id, is_read);

-- Search optimization
CREATE INDEX idx_job_posts_title ON job_posts USING gin(to_tsvector('english', job_title));
CREATE INDEX idx_companies_name ON companies USING gin(to_tsvector('english', company_name));
```

---

## SIMPLE MIGRATION STRATEGY

### Migration 1: Core Tables
```sql
-- Tạo users, companies, job_posts, categories
```

### Migration 2: Application System  
```sql
-- Tạo resumes, applications, saved_jobs
```

### Migration 3: Additional Features
```sql
-- Tạo notifications, reviews, job_categories
```

### Migration 4: Indexes & Constraints
```sql
-- Thêm indexes và constraints
```

---

## SAMPLE DATA STRUCTURE

### Sample Categories:
- Công nghệ thông tin
- Marketing/Sales  
- Kế toán/Tài chính
- Nhân sự
- Thiết kế đồ họa
- Kỹ thuật/Xây dựng

### Sample Job Types:
- Full-time (Toàn thời gian)
- Part-time (Bán thời gian)  
- Contract (Hợp đồng)
- Internship (Thực tập)

### Sample Application Status:
- Pending (Chờ xử lý)
- Reviewing (Đang xem xét)
- Accepted (Được chấp nhận)
- Rejected (Bị từ chối)

---

## CONCLUSION

Database design này đơn giản nhưng đầy đủ chức năng cho một website tuyển dụng cơ bản:

✅ **10 entities** dễ hiểu và maintain
✅ **Core features** đầy đủ cho job portal  
✅ **Simple relationships** dễ implement
✅ **Scalable design** có thể mở rộng sau
✅ **Phù hợp** cho đồ án tốt nghiệp

Thiết kế này tập trung vào functionality cốt lõi mà không quá phức tạp, phù hợp cho việc học tập và demo.