# WORKFINDER SERVER - VISUAL ERD DIAGRAM

## ENTITY RELATIONSHIP DIAGRAM (ERD)

```mermaid
erDiagram
    %% Core Entities
    USER {
        serial user_id PK
        varchar email UK "RFC 5321 compliant"
        varchar password "Hashed"
        varchar full_name
        varchar phone
        text address
        varchar avatar
        enum role "job_seeker, recruiter, admin"
        boolean email_verified
        varchar otp_code
        timestamp otp_expires_at
        enum otp_type
        integer experience_years
        varchar current_position
        varchar linkedin_url
        varchar portfolio_url
        varchar github_url
        enum availability_status
        decimal preferred_min_salary
        decimal preferred_max_salary
        varchar preferred_currency
        enum preferred_pay_period
        timestamp privacy_consent_date
        boolean data_processing_consent
        boolean marketing_consent
        boolean cookie_consent
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
        varchar refresh_token
    }

    COMPANY {
        serial company_id PK
        varchar company_name
        text description
        varchar industry
        varchar website
        varchar logo
        integer employee_count
        integer founding_year
        varchar headquarters
        enum company_type
        text benefits_offered
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    JOBPOST {
        serial job_id PK
        integer company_id FK
        varchar job_title
        text description
        text requirements
        varchar location
        varchar category
        decimal min_salary
        decimal max_salary
        varchar currency
        enum pay_period
        enum job_type
        enum status
        enum experience_level
        boolean remote_allowed
        integer number_of_positions
        enum urgency_level
        timestamp application_deadline
        timestamp posted_date
        integer save_count
        timestamp updated_at
        timestamp deleted_at
    }

    APPLICATION {
        serial application_id PK
        integer job_id FK
        integer user_id FK
        integer resume_id FK
        enum status
        text cover_letter
        decimal expected_salary
        varchar expected_currency
        enum expected_pay_period
        date availability_date
        varchar application_source
        timestamp applied_at
        timestamp updated_at
        timestamp deleted_at
    }

    RESUME {
        serial resume_id PK
        integer user_id FK
        varchar file_name
        varchar file_path
        bigint file_size
        varchar file_type
        varchar mime_type
        varchar original_filename
        varchar file_checksum
        varchar upload_session_id
        boolean is_virus_scanned
        enum virus_scan_result
        timestamp virus_scan_date
        boolean is_processed
        enum processing_status
        text extracted_text
        integer page_count
        enum storage_provider
        varchar external_url
        boolean compression_used
        varchar thumbnail_path
        integer download_count
        timestamp last_downloaded_at
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    INTERVIEW {
        serial interview_id PK
        integer application_id FK
        integer interviewer_id FK
        timestamp interview_time
        integer interview_duration
        varchar interview_location
        enum interview_type
        varchar meeting_link
        enum interview_status
        integer interview_round
        text interview_notes
        text cancelled_reason
        timestamp rescheduled_from
        text feedback
        integer technical_rating
        integer communication_rating
        integer cultural_fit_rating
        integer overall_rating
        enum recommendation
        text next_steps
        date follow_up_date
        timestamp completed_at
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    %% Skills System
    SKILL {
        serial id PK
        varchar name UK
        enum category
        text description
        boolean is_verified
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    JOBSKILL {
        serial id PK
        integer job_id FK
        integer skill_id FK
        boolean is_required
        enum proficiency_level
        integer years_required
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    USERSKILL {
        serial id PK
        integer user_id FK
        integer skill_id FK
        enum proficiency_level
        integer years_experience
        boolean is_endorsed
        date last_used_date
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    %% Notification System
    NOTIFICATIONTYPE {
        serial id PK
        varchar name UK
        text description
        enum category
        enum priority
        boolean default_enabled
        boolean can_be_disabled
        boolean is_active
        varchar template_subject
        text template_body
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    NOTIFICATION {
        serial notification_id PK
        integer recipient_id FK
        integer notification_type_id FK
        text content
        enum priority
        boolean action_required
        enum related_entity_type
        integer related_entity_id
        varchar action_url
        boolean is_read
        timestamp read_at
        timestamp clicked_at
        timestamp expires_at
        boolean email_sent
        timestamp email_sent_at
        boolean push_sent
        timestamp push_sent_at
        integer delivery_attempts
        jsonb metadata
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    USERNOTIFICATIONPREFERENCE {
        serial id PK
        integer user_id FK
        integer notification_type_id FK
        boolean email_enabled
        boolean push_enabled
        boolean in_app_enabled
        enum frequency
        time quiet_hours_start
        time quiet_hours_end
        varchar timezone
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    NOTIFICATIONDELIVERYLOG {
        serial id PK
        integer notification_id FK
        enum delivery_method
        enum delivery_status
        timestamp attempted_at
        timestamp delivered_at
        text error_message
        varchar external_id
        jsonb metadata
        timestamp created_at
    }

    %% Job Management
    SAVEDJOB {
        serial saved_job_id PK
        integer user_id FK
        integer job_id FK
        text notes
        enum priority
        varchar save_reason
        date reminder_date
        varchar tags
        enum interest_level
        boolean application_deadline_reminder
        boolean is_applied
        date applied_date
        date follow_up_date
        integer salary_match_score
        integer location_match_score
        integer skills_match_score
        integer overall_match_score
        timestamp saved_at
        timestamp updated_at
        timestamp deleted_at
    }

    FOLLOWEDCOMPANY {
        serial follow_id PK
        integer user_id FK
        integer company_id FK
        varchar follow_reason
        enum interest_level
        enum priority
        text notes
        varchar tags
        enum notification_frequency
        boolean job_alerts_enabled
        boolean company_updates_enabled
        timestamp last_job_alert_sent
        boolean contact_attempted
        date contact_date
        enum contact_method
        varchar contact_person
        text contact_notes
        timestamp followed_at
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    JOBALERT {
        serial id PK
        integer user_id FK
        varchar name
        enum frequency
        boolean is_active
        text search_keywords
        varchar location
        decimal salary_min
        decimal salary_max
        varchar job_type
        varchar experience_level
        boolean remote_allowed
        varchar categories
        integer[] company_ids
        integer[] excluded_company_ids
        integer[] skill_ids
        timestamp last_sent
        integer jobs_found_count
        integer total_notifications_sent
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    JOBALERTMATCH {
        serial id PK
        integer job_alert_id FK
        integer job_id FK
        integer match_score
        timestamp matched_at
        boolean notification_sent
        timestamp notification_sent_at
    }

    %% Interview Management
    INTERVIEWFEEDBACK {
        serial id PK
        integer interview_id FK
        integer interviewer_id FK
        integer technical_rating
        integer communication_rating
        integer cultural_fit_rating
        integer overall_rating
        text strengths
        text weaknesses
        text feedback_notes
        enum recommendation
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    INTERVIEWQUESTION {
        serial id PK
        text question_text
        enum question_type
        varchar category
        enum difficulty_level
        text expected_answer
        boolean is_active
        integer created_by FK
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    INTERVIEWQUESTIONRESPONSE {
        serial id PK
        integer interview_id FK
        integer question_id FK
        text candidate_answer
        text interviewer_notes
        integer rating
        integer time_spent_minutes
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    %% Supporting Tables
    FILEACCESSLOG {
        serial id PK
        integer file_id
        varchar file_type
        integer user_id FK
        enum access_type
        inet ip_address
        text user_agent
        boolean success
        text error_message
        timestamp accessed_at
    }

    %% Relationships
    %% Core Business Relationships
    USER ||--o{ RESUME : "has"
    USER ||--o{ APPLICATION : "submits"
    USER ||--o{ SAVEDJOB : "saves"
    USER ||--o{ FOLLOWEDCOMPANY : "follows"
    USER ||--o{ NOTIFICATION : "receives"
    USER ||--o{ JOBALERT : "creates"

    COMPANY ||--o{ JOBPOST : "posts"
    COMPANY ||--o{ FOLLOWEDCOMPANY : "followed_by"

    JOBPOST ||--o{ APPLICATION : "receives"
    JOBPOST ||--o{ SAVEDJOB : "saved_as"
    JOBPOST ||--o{ JOBALERTMATCH : "matches"

    APPLICATION ||--o{ INTERVIEW : "leads_to"
    APPLICATION }o--|| RESUME : "uses"

    %% Skills Relationships (Many-to-Many)
    USER ||--o{ USERSKILL : "has"
    SKILL ||--o{ USERSKILL : "possessed_by"
    SKILL ||--o{ JOBSKILL : "required_for"
    JOBPOST ||--o{ JOBSKILL : "requires"

    %% Notification System Relationships
    NOTIFICATIONTYPE ||--o{ NOTIFICATION : "categorizes"
    NOTIFICATIONTYPE ||--o{ USERNOTIFICATIONPREFERENCE : "configured_in"
    USER ||--o{ USERNOTIFICATIONPREFERENCE : "configures"
    NOTIFICATION ||--o{ NOTIFICATIONDELIVERYLOG : "tracked_by"

    %% Interview System Relationships
    INTERVIEW ||--o{ INTERVIEWFEEDBACK : "evaluated_in"
    INTERVIEW ||--o{ INTERVIEWQUESTIONRESPONSE : "answered_in"
    INTERVIEWQUESTION ||--o{ INTERVIEWQUESTIONRESPONSE : "asked_as"
    USER ||--o{ INTERVIEW : "conducts"
    USER ||--o{ INTERVIEWFEEDBACK : "provides"
    USER ||--o{ INTERVIEWQUESTION : "creates"

    %% Job Alert Relationships
    JOBALERT ||--o{ JOBALERTMATCH : "finds"

    %% File Access Logging
    USER ||--o{ FILEACCESSLOG : "accesses"
```

## RELATIONSHIP TYPES LEGEND

### One-to-Many (1:N)
- **User → Resume:** Một user có nhiều resume
- **User → Application:** Một user có nhiều application
- **Company → JobPost:** Một company có nhiều job post
- **JobPost → Application:** Một job có nhiều application
- **Application → Interview:** Một application có nhiều interview

### Many-to-Many (M:N)
- **User ↔ Skill:** User có nhiều skill, skill thuộc về nhiều user
- **JobPost ↔ Skill:** Job yêu cầu nhiều skill, skill được yêu cầu bởi nhiều job
- **User ↔ JobPost:** User save nhiều job, job được save bởi nhiều user
- **User ↔ Company:** User follow nhiều company, company được follow bởi nhiều user

### Self-Referencing
- **User → User:** Interviewer relationship in Interview table

### Advanced Relationships
- **Notification System:** Complex multi-table relationships cho notification management
- **Job Alert System:** Smart matching system với complex filtering
- **File Management:** Security-focused với access logging

## DATABASE CONSTRAINTS SUMMARY

### Primary Keys
- Tất cả tables đều có SERIAL PRIMARY KEY
- Naming convention: `{table}_id` hoặc `id`

### Foreign Keys
- Proper referential integrity với CASCADE options
- ON DELETE CASCADE cho junction tables
- ON DELETE RESTRICT cho critical relationships

### Unique Constraints
- Email trong User table
- Skill name trong Skills table
- Composite unique: (user_id, job_id) trong Applications
- Composite unique: (user_id, skill_id) trong UserSkills

### Check Constraints
- Rating fields: 1-10 range
- Match scores: 0-100 range
- Salary constraints: min <= max
- Date constraints: logical date ordering

### Indexes Strategy
- **Performance indexes:** Tất cả foreign keys
- **Composite indexes:** Complex query patterns
- **Partial indexes:** Soft delete optimization
- **Full-text indexes:** Search functionality
- **GIN indexes:** Array fields và JSON fields

## ADVANCED FEATURES

### Audit Trail
- `created_at`, `updated_at` trên tất cả tables
- Automatic timestamp updates via triggers
- Soft delete với `deleted_at`

### Security
- Password hashing
- File virus scanning
- Access logging
- IP tracking

### Performance
- 50+ optimized indexes
- Full-text search capability
- Query optimization
- Pagination support

### GDPR Compliance
- Privacy consent tracking
- Data processing consent
- Right to be forgotten (soft delete)
- Data export capabilities

Sơ đồ ERD này thể hiện một database design toàn diện cho job platform với tất cả modern features cần thiết.