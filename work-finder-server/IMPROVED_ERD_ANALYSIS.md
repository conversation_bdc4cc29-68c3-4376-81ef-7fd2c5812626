# PHÂN TÍCH VÀ CẢI TIẾN ERD - WORKFINDER SERVER

## 📊 PHÂN TÍCH ERD HIỆN TẠI

### **Đi<PERSON><PERSON> Mạnh của Thiết Kế:**
✅ **Logic rõ ràng** - Flow từ đăng ký → apply → phỏng vấn
✅ **Đầy đủ entities** - Cover hết workflow tuyển dụng  
✅ **Relationships hợp lý** - <PERSON><PERSON><PERSON> mối quan hệ 1:N và N:N đúng
✅ **Practical** - Sát với quy trình thực tế

### **Đề Xuất Cải Tiến:**
🔄 **Thay REVIEW → PHỎNG VẤN** (rất phù hợp!)
🔄 **Tối ưu một số thuộc tính**
🔄 **Thêm constraints cần thiết**

---

## 🎯 THIẾT KẾ MỚI - 10 ENTITIES IMPROVED

### **1. USER (Người dùng)** - *Không đổi*
```sql
user_id: SERIAL PRIMARY KEY
email: VARCHAR(254) NOT NULL UNIQUE
password: VARCHAR(255) NOT NULL
full_name: VARCHAR(150)
phone: VARCHAR(25)
role: ENUM('job_seeker', 'recruiter', 'admin')
avatar: VARCHAR(255)
created_at: TIMESTAMP DEFAULT NOW()
```

### **2. COMPANY (Công ty)** - *Không đổi*
```sql
company_id: SERIAL PRIMARY KEY
company_name: VARCHAR(200) NOT NULL
description: TEXT
industry: VARCHAR(100)
website: VARCHAR(255)
logo: VARCHAR(255)
address: VARCHAR(500)
recruiter_id: INTEGER REFERENCES users(user_id)
created_at: TIMESTAMP DEFAULT NOW()
```

### **3. JOBPOST (Việc cần tuyển)** - *Cải tiến*
```sql
job_id: SERIAL PRIMARY KEY
company_id: INTEGER REFERENCES companies(company_id)
job_title: VARCHAR(200) NOT NULL
description: TEXT NOT NULL
requirements: TEXT
location: VARCHAR(200)
salary_min: DECIMAL(10,2)
salary_max: DECIMAL(10,2)
job_type: ENUM('full_time', 'part_time', 'contract', 'internship')
status: ENUM('active', 'closed', 'draft') DEFAULT 'active'
posted_date: TIMESTAMP DEFAULT NOW()
application_deadline: TIMESTAMP
experience_required: VARCHAR(50) -- VD: 1-3 năm, >5 năm
education_required: VARCHAR(100) -- VD: Đại học, Cao đẳng
```

### **4. CATEGORY (Danh mục)** - *Không đổi*
```sql
category_id: SERIAL PRIMARY KEY
category_name: VARCHAR(100) NOT NULL UNIQUE
description: TEXT
is_active: BOOLEAN DEFAULT true
created_at: TIMESTAMP DEFAULT NOW()
```

### **5. JOBCATEGORY (Junction)** - *Không đổi*
```sql
id: SERIAL PRIMARY KEY
job_id: INTEGER REFERENCES job_posts(job_id)
category_id: INTEGER REFERENCES categories(category_id)
UNIQUE(job_id, category_id)
```

### **6. RESUME (Hồ sơ xin việc)** - *Cải tiến*
```sql
resume_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
file_name: VARCHAR(255) NOT NULL
file_path: VARCHAR(500) NOT NULL
file_size: BIGINT
education: TEXT
experience: TEXT
skills: TEXT
certifications: TEXT -- Chứng chỉ
languages: TEXT -- Ngoại ngữ
is_primary: BOOLEAN DEFAULT false -- CV chính
uploaded_at: TIMESTAMP DEFAULT NOW()
```

### **7. APPLICATION (Đơn ứng tuyển)** - *Cải tiến*
```sql
application_id: SERIAL PRIMARY KEY
job_id: INTEGER REFERENCES job_posts(job_id)
user_id: INTEGER REFERENCES users(user_id)
resume_id: INTEGER REFERENCES resumes(resume_id)
cover_letter: TEXT
status: ENUM('pending', 'reviewing', 'interview_scheduled', 'interviewed', 'accepted', 'rejected')
applied_at: TIMESTAMP DEFAULT NOW()
reviewed_at: TIMESTAMP
notes: TEXT -- Ghi chú từ HR
UNIQUE(user_id, job_id)
```

### **8. SAVEDJOB (Việc đã lưu)** - *Không đổi*
```sql
saved_job_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
job_id: INTEGER REFERENCES job_posts(job_id)
saved_at: TIMESTAMP DEFAULT NOW()
UNIQUE(user_id, job_id)
```

### **9. NOTIFICATION (Thông báo)** - *Cải tiến*
```sql
notification_id: SERIAL PRIMARY KEY
user_id: INTEGER REFERENCES users(user_id)
title: VARCHAR(255) NOT NULL
message: TEXT NOT NULL
type: ENUM('application_status', 'interview_invite', 'new_job', 'system')
related_id: INTEGER -- ID liên quan (application_id, interview_id...)
is_read: BOOLEAN DEFAULT false
created_at: TIMESTAMP DEFAULT NOW()
```

### **🆕 10. INTERVIEW (Phỏng vấn)** - *Thay thế Review*
```sql
interview_id: SERIAL PRIMARY KEY
application_id: INTEGER REFERENCES applications(application_id)
interviewer_id: INTEGER REFERENCES users(user_id) -- Người phỏng vấn
interview_date: TIMESTAMP NOT NULL
interview_time_duration: INTEGER DEFAULT 60 -- Phút
interview_type: ENUM('in_person', 'online', 'phone') DEFAULT 'in_person'
interview_address: VARCHAR(500) -- Địa điểm (nếu trực tiếp)
meeting_link: VARCHAR(500) -- Link meeting (nếu online)
status: ENUM('scheduled', 'completed', 'cancelled', 'rescheduled') DEFAULT 'scheduled'

-- Đánh giá sau phỏng vấn
technical_score: INTEGER CHECK (technical_score >= 1 AND technical_score <= 10)
communication_score: INTEGER CHECK (communication_score >= 1 AND communication_score <= 10)
attitude_score: INTEGER CHECK (attitude_score >= 1 AND attitude_score <= 10)
overall_rating: ENUM('excellent', 'good', 'average', 'poor')
interviewer_notes: TEXT
candidate_feedback: TEXT -- Feedback từ ứng viên về buổi phỏng vấn

-- Audit
created_at: TIMESTAMP DEFAULT NOW()
updated_at: TIMESTAMP DEFAULT NOW()
```

---

## 🔗 UPDATED RELATIONSHIPS

```
USER (1) ─────→ (N) COMPANY [as recruiter]
USER (1) ─────→ (N) RESUME
USER (1) ─────→ (N) APPLICATION  
USER (1) ─────→ (N) SAVEDJOB
USER (1) ─────→ (N) NOTIFICATION
USER (1) ─────→ (N) INTERVIEW [as interviewer] 🆕

COMPANY (1) ──→ (N) JOBPOST

JOBPOST (1) ──→ (N) APPLICATION
JOBPOST (1) ──→ (N) SAVEDJOB
JOBPOST (N) ←─→ (N) CATEGORY [via JOBCATEGORY]

APPLICATION (1) → (N) INTERVIEW 🆕
RESUME (1) ────→ (N) APPLICATION
```

---

## 🎯 BUSINESS FLOW MỚI

### **Complete Recruitment Process:**

1. **Recruiter** đăng job posting
2. **Job Seeker** tìm kiếm và save job yêu thích
3. **Job Seeker** upload resume và apply job
4. **Recruiter** review applications 
5. **Recruiter** schedule interview cho ứng viên phù hợp
6. **Interview** được thực hiện (in-person/online/phone)
7. **Interviewer** đánh giá và ghi notes
8. **Application status** được update dựa trên kết quả interview
9. **Notifications** được gửi tự động ở mỗi bước

---

## 🆕 NEW FEATURES WITH INTERVIEW TABLE

### **Interview Management:**
✅ **Schedule interviews** - Lên lịch phỏng vấn
✅ **Multiple interview types** - Trực tiếp, online, điện thoại
✅ **Interview evaluation** - Đánh giá chi tiết theo từng tiêu chí
✅ **Bidirectional feedback** - Cả interviewer và candidate đều feedback
✅ **Interview history** - Lịch sử phỏng vấn đầy đủ

### **Enhanced Application Flow:**
✅ **Interview-driven status** - Status dựa trên kết quả phỏng vấn
✅ **Detailed evaluation** - Technical, Communication, Attitude scores
✅ **Notes & Documentation** - Ghi chú chi tiết quá trình

### **Better User Experience:**
✅ **Interview notifications** - Thông báo lịch phỏng vấn
✅ **Calendar integration ready** - Sẵn sàng tích hợp calendar
✅ **Professional workflow** - Quy trình chuyên nghiệp

---

## 📊 UPDATED SAMPLE DATA

### **Interview Types:**
- `in_person` - Phỏng vấn trực tiếp
- `online` - Phỏng vấn online (Zoom, Teams...)
- `phone` - Phỏng vấn qua điện thoại

### **Interview Status:**
- `scheduled` - Đã lên lịch
- `completed` - Đã hoàn thành
- `cancelled` - Đã hủy
- `rescheduled` - Đã dời lịch

### **Overall Rating:**
- `excellent` - Xuất sắc
- `good` - Tốt  
- `average` - Trung bình
- `poor` - Kém

### **Enhanced Application Status:**
- `pending` - Chờ xử lý
- `reviewing` - Đang xem xét
- `interview_scheduled` - Đã lên lịch phỏng vấn
- `interviewed` - Đã phỏng vấn
- `accepted` - Được nhận
- `rejected` - Bị từ chối

---

## 🔧 IMPLEMENTATION UPDATES

### **New Migration Required:**
```sql
-- Replace reviews table with interviews
DROP TABLE IF EXISTS reviews CASCADE;
CREATE TABLE interviews (
  interview_id SERIAL PRIMARY KEY,
  application_id INTEGER REFERENCES applications(application_id),
  interviewer_id INTEGER REFERENCES users(user_id),
  interview_date TIMESTAMP NOT NULL,
  interview_time_duration INTEGER DEFAULT 60,
  interview_type VARCHAR(20) DEFAULT 'in_person' 
    CHECK (interview_type IN ('in_person', 'online', 'phone')),
  interview_address VARCHAR(500),
  meeting_link VARCHAR(500),
  status VARCHAR(20) DEFAULT 'scheduled'
    CHECK (status IN ('scheduled', 'completed', 'cancelled', 'rescheduled')),
  technical_score INTEGER CHECK (technical_score >= 1 AND technical_score <= 10),
  communication_score INTEGER CHECK (communication_score >= 1 AND communication_score <= 10),
  attitude_score INTEGER CHECK (attitude_score >= 1 AND attitude_score <= 10),
  overall_rating VARCHAR(20) CHECK (overall_rating IN ('excellent', 'good', 'average', 'poor')),
  interviewer_notes TEXT,
  candidate_feedback TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_interviews_application ON interviews(application_id);
CREATE INDEX idx_interviews_interviewer ON interviews(interviewer_id);
CREATE INDEX idx_interviews_date ON interviews(interview_date);
CREATE INDEX idx_interviews_status ON interviews(status);
```

---

## 🎓 ĐÁNH GIÁ THIẾT KẾ

### **Độ Phù Hợp Cho Đồ Án:** ⭐⭐⭐⭐⭐

✅ **Realistic Workflow** - Workflow thực tế của tuyển dụng
✅ **Professional Features** - Tính năng chuyên nghiệp
✅ **Manageable Complexity** - Vẫn đủ đơn giản cho đồ án
✅ **Educational Value** - Giá trị học tập cao
✅ **Scalable Design** - Dễ mở rộng

### **Lợi Ích Của Thay Đổi:**

🎯 **More Relevant** - Interview quan trọng hơn review trong tuyển dụng
🎯 **Complete Process** - Bao phủ toàn bộ quy trình tuyển dụng
🎯 **Professional Touch** - Tăng tính chuyên nghiệp của hệ thống
🎯 **Better Demo** - Demo được quy trình hoàn chỉnh
🎯 **Real-world Application** - Sát với thực tế doanh nghiệp

---

## 🚀 RECOMMENDATION

**Highly Recommend** việc thay thế Review bằng Interview table vì:

1. **More Logical** - Interview là bước thiết yếu trong tuyển dụng
2. **Better User Stories** - Có thể demo complete recruitment flow
3. **Professional Standard** - Đạt chuẩn các ATS (Applicant Tracking System) thực tế
4. **Rich Features** - Nhiều tính năng để develop và demo
5. **Academic Value** - Thể hiện hiểu biết sâu về business domain

Thiết kế này sẽ giúp đồ án nổi bật và thể hiện tư duy system design mature! 🎯