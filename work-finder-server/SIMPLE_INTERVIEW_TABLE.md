# SIMPLE INTERVIEW TABLE - CHỈ ĐỂ LÊN LỊCH PHỎNG VẤN

## 🎯 MỤC ĐÍCH
Bảng Interview **chỉ để lên lịch phỏng vấn** cho ứng viên, không phải hệ thống đánh giá phức tạp.

---

## 📋 THIẾT KẾ ĐỠN GIẢN

### **INTERVIEW Table (Simplified)**
```sql
interview_id: SERIAL PRIMARY KEY
application_id: INTEGER REFERENCES applications(application_id) -- <PERSON><PERSON><PERSON> kết với đơn ứng tuyển
interviewer_name: VARCHAR(150) -- Tên người phỏng vấn (có thể không phải user trong hệ thống)
interview_date: DATE NOT NULL -- Ngày phỏng vấn
interview_time: TIME NOT NULL -- Giờ phỏng vấn
interview_location: VARCHAR(300) -- Đ<PERSON><PERSON> điểm (địa chỉ hoặc link meeting)
interview_type: ENUM('offline', 'online') DEFAULT 'offline' -- Tr<PERSON><PERSON> tiế<PERSON> hay online
notes: TEXT -- Ghi chú thêm
status: ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled' -- Trạng thái lịch hẹn
created_at: TIMESTAMP DEFAULT NOW()
```

---

## 🔍 PHÂN TÍCH THIẾT KẾ

### **✅ Đủ Chức Năng Cho Website Đơn Giản:**

1. **Lên lịch phỏng vấn** - Recruiter tạo lịch hẹn
2. **Thông tin cơ bản** - Ngày, giờ, địa điểm, người phỏng vấn
3. **Linh hoạt** - interviewer_name không bắt buộc phải là user
4. **Đơn giản** - Chỉ 3 status: scheduled/completed/cancelled
5. **Practical** - Đủ dùng cho most cases

### **✅ Loại Bỏ Các Phần Phức Tạp:**
- ❌ Không có evaluation scores
- ❌ Không có multiple feedback fields
- ❌ Không có interview duration tracking
- ❌ Không có complex meeting management

---

## 🚀 BUSINESS FLOW ĐƠN GIẢN

### **1. Application Submitted**
```
User apply job → Application status: "pending"
```

### **2. Application Reviewed**  
```
Recruiter review → Application status: "reviewing"
```

### **3. Interview Scheduled**
```
Recruiter tạo interview record → Application status: "interview_scheduled"
```

### **4. Interview Conducted**
```
Interview status: "completed" → Application status: "interviewed"
```

### **5. Final Decision**
```
Recruiter decide → Application status: "accepted"/"rejected"
```

---

## 📊 SAMPLE DATA

### **Interview Types:**
- `offline` - Phỏng vấn trực tiếp tại công ty
- `online` - Phỏng vấn qua video call

### **Interview Status:**
- `scheduled` - Đã lên lịch
- `completed` - Đã hoàn thành  
- `cancelled` - Đã hủy

### **Sample Interview Records:**
```sql
INSERT INTO interviews VALUES
(1, 101, 'Nguyễn Văn A - HR Manager', '2024-02-15', '14:00', 'Phòng họp tầng 3, Tòa nhà ABC', 'offline', 'Mang theo CV gốc', 'scheduled'),
(2, 102, 'Trần Thị B - Tech Lead', '2024-02-16', '10:00', 'https://meet.google.com/abc-xyz', 'online', 'Interview kỹ thuật, chuẩn bị code test', 'scheduled');
```

---

## 🔧 CẢI TIẾN SO VỚI THIẾT KẾ TRƯỚC

### **Before (Phức Tạp):**
```sql
-- Quá nhiều fields không cần thiết cho website đơn giản
interviewer_id: INTEGER FK → users(user_id)  -- Bắt buộc phải user
interview_time_duration: INTEGER
technical_score: INTEGER
communication_score: INTEGER  
attitude_score: INTEGER
overall_rating: ENUM
interviewer_notes: TEXT
candidate_feedback: TEXT
meeting_link: VARCHAR(500)
interview_address: VARCHAR(500)
```

### **After (Đơn Giản):**
```sql
-- Chỉ giữ essentials
interviewer_name: VARCHAR(150)  -- Flexible, không bắt buộc FK
interview_date: DATE
interview_time: TIME
interview_location: VARCHAR(300)  -- Unified field
interview_type: ENUM('offline', 'online')
notes: TEXT  -- Single notes field
status: ENUM('scheduled', 'completed', 'cancelled')
```

---

## 💡 LÝ DO THIẾT KẾ ĐƠN GIẢN HƠN

### **1. Phù Hợp Mục Tiêu:**
- Website tuyển dụng **đơn giản**
- Đồ án tốt nghiệp **không cần quá phức tạp**
- Focus vào **core functionality**

### **2. Thực Tế Sử Dụng:**
- Nhiều công ty **interviewer không phải user** trong hệ thống
- **Đánh giá phỏng vấn** thường làm offline, không cần store trong DB
- **Lên lịch** là function chính cần thiết

### **3. Dễ Implement:**
- Ít validation logic
- UI đơn giản hơn
- Ít edge cases

### **4. Scalable:**
- Có thể thêm fields sau nếu cần
- Foundation solid cho future enhancement

---

## 🎯 CHỨC NĂNG CORE

### **For Recruiters:**
✅ **Tạo lịch phỏng vấn** cho application
✅ **Chọn ngày/giờ** phù hợp
✅ **Nhập thông tin** người phỏng vấn  
✅ **Chỉ định địa điểm** hoặc link meeting
✅ **Quản lý status** lịch hẹn
✅ **Thêm notes** nếu cần

### **For Candidates:**
✅ **Xem lịch phỏng vấn** được assign
✅ **Biết thông tin** ngày/giờ/địa điểm
✅ **Confirm attendance** (có thể thêm field later)

### **For System:**
✅ **Send notifications** khi có lịch mới
✅ **Update application status** automatically
✅ **Track interview history**

---

## 🔄 CẬP NHẬT APPLICATION STATUS

```sql
-- Trigger để auto-update application status
CREATE OR REPLACE FUNCTION update_application_status_on_interview()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Khi tạo interview mới
    UPDATE applications 
    SET status = 'interview_scheduled' 
    WHERE application_id = NEW.application_id;
  ELSIF TG_OP = 'UPDATE' AND NEW.status = 'completed' THEN
    -- Khi interview completed
    UPDATE applications 
    SET status = 'interviewed' 
    WHERE application_id = NEW.application_id;
  END IF;
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER interview_status_trigger
  AFTER INSERT OR UPDATE ON interviews
  FOR EACH ROW
  EXECUTE FUNCTION update_application_status_on_interview();
```

---

## 📋 MIGRATION UPDATE

```sql
-- Simplified Interview Table
CREATE TABLE interviews (
  interview_id SERIAL PRIMARY KEY,
  application_id INTEGER NOT NULL REFERENCES applications(application_id) ON DELETE CASCADE,
  interviewer_name VARCHAR(150) NOT NULL,
  interview_date DATE NOT NULL,
  interview_time TIME NOT NULL,
  interview_location VARCHAR(300) NOT NULL,
  interview_type VARCHAR(10) DEFAULT 'offline' CHECK (interview_type IN ('offline', 'online')),
  notes TEXT,
  status VARCHAR(15) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Essential indexes
CREATE INDEX idx_interviews_application ON interviews(application_id);
CREATE INDEX idx_interviews_date ON interviews(interview_date);
CREATE INDEX idx_interviews_status ON interviews(status);
```

---

## 🎓 KẾT LUẬN

**Interview table đơn giản này PERFECT cho đồ án tốt nghiệp** vì:

✅ **Đủ chức năng** - Cover interview scheduling needs
✅ **Không overcomplicated** - Phù hợp scope đồ án
✅ **Easy to implement** - Frontend/Backend đơn giản
✅ **Professional** - Vẫn thể hiện understanding về recruitment flow
✅ **Practical** - Sát với thực tế nhiều công ty nhỏ

Thiết kế này **balanced** giữa functionality và simplicity, perfect cho academic project! 🎯