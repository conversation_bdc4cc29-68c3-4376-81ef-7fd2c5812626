import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Request,
  Response,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  Response as ExpressResponse,
  Request as ExpressRequest,
} from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { ResponseMessage } from '../common/interceptors/response.interceptor';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @ResponseMessage('User registered successfully')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'User registered successfully',
    schema: {
      example: {
        success: true,
        status: 201,
        data: {
          user: {
            user_id: 1,
            username: 'john_doe',
            full_name: 'John Doe',
            email: '<EMAIL>',
            role: 'job_seeker',
          },
        },
        message: 'User registered successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 409, description: 'Username or email already exists' })
  async register(
    @Body() registerDto: RegisterDto,
    @Response() res: ExpressResponse,
  ) {
    const authResult = await this.authService.register(registerDto);

    return res.json({
      success: true,
      status: 201,
      data: authResult,
      message: 'User registered successfully. Please verify your email.',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('User logged in successfully')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User logged in successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: {
          user: {
            user_id: 1,
            username: 'john_doe',
            full_name: 'John Doe',
            email: '<EMAIL>',
            role: 'job_seeker',
          },
        },
        message: 'User logged in successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(
    @Body() loginDto: LoginDto,
    @Request() req: ExpressRequest,
    @Response() res: ExpressResponse,
  ) {
    const authResult = await this.authService.login(req.user);

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
    };

    res.cookie('access_token', authResult.access_token, {
      ...cookieOptions,
      maxAge: 60 * 60 * 1000, // 1 hour
    });

    res.cookie('refresh_token', authResult.refresh_token, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    return res.json({
      success: true,
      status: 200,
      data: {
        user: authResult.user,
      },
      message: 'User logged in successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'Token refreshed successfully' },
        message: 'Token refreshed successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(
    @Request() req: ExpressRequest,
    @Response() res: ExpressResponse,
  ) {
    const refreshToken = req.cookies['refresh_token'];
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        status: 401,
        message: 'Refresh token not found',
        timestamp: new Date().toISOString(),
        error: {
          code: 'UNAUTHORIZED',
          details: 'Refresh token not found in cookies',
        },
      });
    }

    const result = await this.authService.refreshToken(refreshToken);

    res.cookie('access_token', result.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
      maxAge: 60 * 60 * 1000, // 1 hour
    });

    return res.json({
      success: true,
      status: 200,
      data: { message: 'Token refreshed successfully' },
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 200, description: 'User logged out successfully' })
  @ApiBearerAuth()
  async logout(@CurrentUser() user: any, @Response() res: ExpressResponse) {
    await this.authService.logout(user.user_id);
    const clearCookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
    };

    res.clearCookie('access_token', clearCookieOptions);
    res.clearCookie('refresh_token', clearCookieOptions);

    return res.json({
      success: true,
      status: 200,
      data: { message: 'User logged out successfully' },
      message: 'User logged out successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Current user profile' })
  @ApiBearerAuth()
  async getProfile(@CurrentUser() user: any, @Request() req: ExpressRequest) {
    return { user };
  }

  @UseGuards(JwtAuthGuard)
  @Post('send-otp')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('OTP sent successfully')
  @ApiOperation({ summary: 'Send OTP for email verification' })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'OTP sent successfully' },
        message: 'OTP sent successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'User email is required or already verified' })
  @ApiBearerAuth()
  async sendOTP(@CurrentUser() user: any) {
    return this.authService.sendOTP(user.user_id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Email verified successfully')
  @ApiOperation({ summary: 'Verify OTP code' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'Email verified successfully' },
        message: 'Email verified successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid or expired OTP code' })
  @ApiBearerAuth()
  async verifyOTP(@CurrentUser() user: any, @Body() verifyOtpDto: VerifyOtpDto) {
    return this.authService.verifyOTP(user.user_id, verifyOtpDto.otp_code);
  }

  @UseGuards(JwtAuthGuard)
  @Post('resend-otp')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('OTP resent successfully')
  @ApiOperation({ summary: 'Resend OTP for email verification' })
  @ApiResponse({
    status: 200,
    description: 'OTP resent successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'OTP sent successfully' },
        message: 'OTP resent successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'User email is required or already verified' })
  @ApiBearerAuth()
  async resendOTP(@CurrentUser() user: any) {
    return this.authService.resendOTP(user.user_id);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Password reset OTP sent successfully')
  @ApiOperation({ summary: 'Send password reset OTP to email' })
  @ApiResponse({
    status: 200,
    description: 'Password reset OTP sent successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'Password reset OTP sent successfully' },
        message: 'Password reset OTP sent successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'User not found or email not verified' })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.sendPasswordResetOTP(forgotPasswordDto.email);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('Password reset successfully')
  @ApiOperation({ summary: 'Reset password with OTP' })
  @ApiResponse({
    status: 200,
    description: 'Password reset successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'Password reset successfully' },
        message: 'Password reset successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid or expired OTP code' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.email,
      resetPasswordDto.otp_code,
      resetPasswordDto.new_password,
    );
  }
}
