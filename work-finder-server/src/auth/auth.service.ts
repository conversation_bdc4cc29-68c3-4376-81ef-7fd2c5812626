import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { User } from '../users/entities/user.entity';
import { RegisterDto } from './dto/register.dto';
import { UserRole } from '../common/enums/user-role.enum';
import { OtpType } from '../common/enums/otp-type.enum';
import { MailService } from '../mail/mail.service';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailService: MailService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      return null;
    }

    if (!user.email_verified) {
      throw new UnauthorizedException('Email not verified. Please verify your email first.');
    }

    if (await bcrypt.compare(password, user.password)) {
      const { password: pwd, refresh_token, otp_code, otp_expires_at, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: any) {
    const payload = {
      email: user.email,
      sub: user.user_id,
      role: user.role,
    };

    const access_token = this.jwtService.sign(payload);
    const refresh_token = this.jwtService.sign(payload, { expiresIn: '7d' });

    // Save refresh token to database
    await this.userRepository.update(user.user_id, { refresh_token });

    return {
      access_token,
      refresh_token,
      user: {
        user_id: user.user_id,
        username: user.username,
        full_name: user.full_name,
        email: user.email,
        role: user.role,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    // Check if username already exists
    const existingUser = await this.userRepository.findOne({
      where: { username: registerDto.username },
    });

    if (existingUser) {
      throw new ConflictException('Username already exists');
    }

    // Check if email already exists
    const existingEmail = await this.userRepository.findOne({
      where: { email: registerDto.email },
    });

    if (existingEmail) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    // Create user with email_verified: false
    const user = this.userRepository.create({
      ...registerDto,
      password: hashedPassword,
      role: registerDto.role || UserRole.JOB_SEEKER,
      email_verified: false,
    });

    const savedUser = await this.userRepository.save(user);

    // Send OTP immediately after user creation
    const otpResult = await this.sendOTP(savedUser.user_id);
    
    if (!otpResult) {
      // If OTP sending fails, still return success but notify user to check email
      console.warn('Failed to send OTP during registration for user:', savedUser.email);
    }

    return {
      message: 'Registration successful. Please check your email for verification code.',
      user: {
        user_id: savedUser.user_id,
        username: savedUser.username,
        full_name: savedUser.full_name,
        email: savedUser.email,
        role: savedUser.role,
        email_verified: savedUser.email_verified,
      },
      requires_verification: true,
    };
  }

  async refreshToken(refreshToken: string) {
    try {
      const payload = this.jwtService.verify(refreshToken);
      const user = await this.userRepository.findOne({
        where: { user_id: payload.sub, refresh_token: refreshToken },
      });

      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const newPayload = {
        username: user.username,
        sub: user.user_id,
        role: user.role,
      };

      const access_token = this.jwtService.sign(newPayload);

      return { access_token };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: number) {
    await this.userRepository.update(userId, { refresh_token: undefined });
    return { message: 'Logged out successfully' };
  }

  private generateOTP(): string {
    const length = this.configService.get('OTP_LENGTH', 6);
    return Math.floor(Math.random() * Math.pow(10, length))
      .toString()
      .padStart(length, '0');
  }

  async sendOTP(userId: number) {
    const user = await this.userRepository.findOne({
      where: { user_id: userId },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.email) {
      throw new BadRequestException('User email is required for OTP verification');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email is already verified');
    }

    const otpCode = this.generateOTP();
    const expiryMinutes = this.configService.get('OTP_EXPIRY_MINUTES', 5);
    const otpExpiresAt = new Date();
    otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + expiryMinutes);

    await this.userRepository.update(userId, {
      otp_code: otpCode,
      otp_expires_at: otpExpiresAt,
      otp_type: OtpType.REGISTRATION,
    });

    const emailResult = await this.mailService.sendOtpVerification(
      user.email,
      user.full_name || user.username,
      otpCode,
    );

    if (!emailResult.success) {
      throw new BadRequestException('Failed to send OTP email');
    }

    return { message: 'OTP sent successfully' };
  }

  async verifyOTP(userId: number, otpCode: string) {
    const user = await this.userRepository.findOne({
      where: { user_id: userId },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email is already verified');
    }

    if (!user.otp_code || !user.otp_expires_at) {
      throw new BadRequestException('No OTP found. Please request a new one');
    }

    if (new Date() > user.otp_expires_at) {
      throw new BadRequestException('OTP has expired. Please request a new one');
    }

    if (user.otp_code !== otpCode) {
      throw new BadRequestException('Invalid OTP code');
    }

    await this.userRepository.update(userId, {
      email_verified: true,
      otp_code: undefined,
      otp_expires_at: undefined,
      otp_type: undefined,
    });

    // Send welcome email
    if (user.email) {
      await this.mailService.sendWelcomeEmail(
        user.email,
        user.full_name || user.username,
      );
    }

    return { message: 'Email verified successfully' };
  }

  async resendOTP(userId: number) {
    const user = await this.userRepository.findOne({
      where: { user_id: userId },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email is already verified');
    }

    return this.sendOTP(userId);
  }

  async sendPasswordResetOTP(email: string) {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new BadRequestException('User with this email does not exist');
    }

    if (!user.email_verified) {
      throw new BadRequestException('Email not verified. Please verify your email first.');
    }

    const otpCode = this.generateOTP();
    const expiryMinutes = this.configService.get('OTP_EXPIRY_MINUTES', 5);
    const otpExpiresAt = new Date();
    otpExpiresAt.setMinutes(otpExpiresAt.getMinutes() + expiryMinutes);

    await this.userRepository.update(user.user_id, {
      otp_code: otpCode,
      otp_expires_at: otpExpiresAt,
      otp_type: OtpType.PASSWORD_RESET,
    });

    const emailResult = await this.mailService.sendPasswordResetOTP(
      user.email!,
      user.full_name || user.username,
      otpCode,
    );

    if (!emailResult.success) {
      throw new BadRequestException('Failed to send password reset email');
    }

    return { message: 'Password reset OTP sent successfully' };
  }

  async resetPassword(email: string, otpCode: string, newPassword: string) {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new BadRequestException('User with this email does not exist');
    }

    if (!user.otp_code || !user.otp_expires_at) {
      throw new BadRequestException('No password reset OTP found. Please request a new one');
    }

    if (user.otp_type !== OtpType.PASSWORD_RESET) {
      throw new BadRequestException('Invalid OTP type. Please request a password reset OTP');
    }

    if (new Date() > user.otp_expires_at) {
      throw new BadRequestException('OTP has expired. Please request a new one');
    }

    if (user.otp_code !== otpCode) {
      throw new BadRequestException('Invalid OTP code');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and clear OTP
    await this.userRepository.update(user.user_id, {
      password: hashedPassword,
      otp_code: undefined,
      otp_expires_at: undefined,
      otp_type: undefined,
      refresh_token: undefined, // Clear all sessions
    });

    // Send password changed notification email
    if (user.email) {
      await this.mailService.sendPasswordChangedNotification(
        user.email,
        user.full_name || user.username,
      );
    }

    return { message: 'Password reset successfully' };
  }
}
