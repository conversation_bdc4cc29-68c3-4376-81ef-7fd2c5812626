-- Migration: Add email verification and notification fields to users table
-- Date: 2024-01-01
-- Description: Add fields for email verification (OTP) and email notification preferences

-- Add email verification fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_code VARCHAR(10);
ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_expires_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_type VARCHAR(20) CHECK (otp_type IN ('registration', 'password_reset'));
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_notifications_enabled BOOLEAN DEFAULT TRUE;

-- Add comments for documentation
COMMENT ON COLUMN users.email_verified IS 'Indicates if user email has been verified via OTP';
COMMENT ON COLUMN users.otp_code IS 'Current OTP code for email verification';
COMMENT ON COLUMN users.otp_expires_at IS 'Expiration timestamp for the OTP code';
COMMENT ON COLUMN users.otp_type IS 'Type of OTP: registration, password_reset';
COMMENT ON COLUMN users.email_notifications_enabled IS 'User preference for receiving job notification emails';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_email_notifications ON users(email_notifications_enabled);
CREATE INDEX IF NOT EXISTS idx_users_otp_expires ON users(otp_expires_at);

-- Update existing users to have email_verified = true if they have email
-- (for backward compatibility)
UPDATE users 
SET email_verified = TRUE 
WHERE email IS NOT NULL AND email != '' AND email_verified IS NULL;

-- Update existing users to have email notifications enabled by default
UPDATE users 
SET email_notifications_enabled = TRUE 
WHERE email_notifications_enabled IS NULL;