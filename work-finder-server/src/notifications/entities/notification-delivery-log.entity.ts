import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Notification } from './notification.entity';

export enum DeliveryMethod {
  EMAIL = 'email',
  PUSH = 'push',
  SMS = 'sms',
  IN_APP = 'in_app',
}

export enum DeliveryStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  BOUNCED = 'bounced',
}

@Entity('notification_delivery_log')
export class NotificationDeliveryLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  notification_id: number;

  @Column({
    type: 'enum',
    enum: DeliveryMethod,
  })
  delivery_method: DeliveryMethod;

  @Column({
    type: 'enum',
    enum: DeliveryStatus,
  })
  delivery_status: DeliveryStatus;

  @CreateDateColumn()
  attempted_at: Date;

  @Column({ nullable: true })
  delivered_at?: Date;

  @Column({ type: 'text', nullable: true })
  error_message?: string;

  @Column({ length: 255, nullable: true })
  external_id?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  @CreateDateColumn()
  created_at: Date;

  // Relations
  @ManyToOne(() => Notification, (notification) => notification.delivery_logs, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'notification_id' })
  notification: Notification;
}
