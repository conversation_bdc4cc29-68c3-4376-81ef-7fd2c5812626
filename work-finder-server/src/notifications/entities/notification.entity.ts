import {
  En<PERSON>ty,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import {
  NotificationType,
  NotificationPriority,
} from './notification-type.entity';
import { NotificationDeliveryLog } from './notification-delivery-log.entity';

export enum RelatedEntityType {
  JOB_POST = 'job_post',
  APPLICATION = 'application',
  INTERVIEW = 'interview',
  COMPANY = 'company',
  USER = 'user',
  RESUME = 'resume',
}

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn()
  notification_id: number;

  @Column({ name: 'recipient_id' })
  recipient_id: number;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'is_read', default: false })
  is_read: boolean;

  @Column({ nullable: true })
  notification_type_id?: number;

  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.NORMAL,
  })
  priority: NotificationPriority;

  @Column({ default: false })
  action_required: boolean;

  @Column({
    type: 'enum',
    enum: RelatedEntityType,
    nullable: true,
  })
  related_entity_type?: RelatedEntityType;

  @Column({ nullable: true })
  related_entity_id?: number;

  @Column({ length: 500, nullable: true })
  action_url?: string;

  @Column({ nullable: true })
  expires_at?: Date;

  @Column({ nullable: true })
  read_at?: Date;

  @Column({ nullable: true })
  clicked_at?: Date;

  @Column({ default: false })
  email_sent: boolean;

  @Column({ nullable: true })
  email_sent_at?: Date;

  @Column({ default: false })
  push_sent: boolean;

  @Column({ nullable: true })
  push_sent_at?: Date;

  @Column({ default: 0 })
  delivery_attempts: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.notifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'recipient_id' })
  recipient: User;

  @ManyToOne(
    () => NotificationType,
    (notificationType) => notificationType.notifications,
  )
  @JoinColumn({ name: 'notification_type_id' })
  notification_type?: NotificationType;

  @OneToMany(
    () => NotificationDeliveryLog,
    (deliveryLog) => deliveryLog.notification,
  )
  delivery_logs: NotificationDeliveryLog[];
}
