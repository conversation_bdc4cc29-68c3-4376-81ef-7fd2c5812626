import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { Notification } from './entities/notification.entity';
import { User } from '../users/entities/user.entity';
import { JobPost } from '../jobs/entities/job-post.entity';
import { MailModule } from '../mail/mail.module';

// Services
import { NotificationsService } from './services/notifications.service';
import { EmailNotificationsService } from './services/email-notifications.service';

// Controllers
import { NotificationsController } from './controllers/notifications.controller';
import { EmailNotificationsController } from './controllers/email-notifications.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notification, User, JobPost]),
    ScheduleModule.forRoot(),
    MailModule,
  ],
  controllers: [
    NotificationsController,
    EmailNotificationsController,
  ],
  providers: [
    NotificationsService,
    EmailNotificationsService,
  ],
  exports: [
    NotificationsService,
    EmailNotificationsService,
    TypeOrmModule,
  ],
})
export class NotificationsModule {}
