import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { JobSkill } from './job-skill.entity';
import { UserSkill } from './user-skill.entity';

export enum SkillCategory {
  TECHNICAL = 'technical',
  SOFT = 'soft',
  LANGUAGE = 'language',
  CERTIFICATION = 'certification',
  TOOL = 'tool',
  FRAMEWORK = 'framework',
  METHODOLOGY = 'methodology',
}

@Entity('skills')
export class Skill {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, unique: true })
  name: string;

  @Column({
    type: 'enum',
    enum: SkillCategory,
    nullable: true,
  })
  category?: SkillCategory;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ default: false })
  is_verified: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @OneToMany(() => JobSkill, (jobSkill) => jobSkill.skill)
  job_skills: JobSkill[];

  @OneToMany(() => UserSkill, (userSkill) => userSkill.skill)
  user_skills: UserSkill[];
}
