import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { Skill } from './skill.entity';
import { User } from '../../users/entities/user.entity';
import { ProficiencyLevel } from './job-skill.entity';

@Entity('user_skills')
@Unique(['user_id', 'skill_id'])
export class UserSkill {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  skill_id: number;

  @Column({
    type: 'enum',
    enum: ProficiencyLevel,
    default: ProficiencyLevel.INTERMEDIATE,
  })
  proficiency_level: ProficiencyLevel;

  @Column({ nullable: true })
  years_experience?: number;

  @Column({ default: false })
  is_endorsed: boolean;

  @Column({ type: 'date', nullable: true })
  last_used_date?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.user_skills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Skill, (skill) => skill.user_skills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'skill_id' })
  skill: Skill;
}
