{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm create:*)", "Bash(npm install)", "Bash(npm install:*)", "Bash(npx tailwindcss:*)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(npx tsc:*)", "Bash(npx shadcn:*)", "Bash(npm run lint)", "WebFetch(domain:github.com)", "WebFetch(domain:feature-sliced.design)", "Bash(find:*)", "Bash(cp:*)", "Bash(npm run lint:*)", "<PERSON><PERSON>(true)", "Bash(npm run typeorm:show:*)", "Bash(npm run:*)", "Bash(npx typeorm-ts-node-commonjs migration:run:*)", "Bash(psql:*)", "Bash(PGPASSWORD=012003 psql -h localhost -U postgres -c \"CREATE DATABASE work_finder;\")", "Bash(PGPASSWORD=012003 psql -h localhost -U postgres -d work_finder -c \"\\dt\")", "Bash(PGPASSWORD=012003 psql:*)"], "deny": []}}